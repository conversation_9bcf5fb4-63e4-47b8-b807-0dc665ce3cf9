<script setup lang="ts">
import type { FormInstance } from 'vant'
import { getConfigByName, runLogic } from '@af-mobile-client-vue3/services/api/common'

import {
  FloatingBubble,
  showConfirmDialog,
  showFailToast,
  Button as <PERSON><PERSON><PERSON><PERSON>,
  Form as VanForm,
  Icon as <PERSON><PERSON><PERSON>,
  Popover as <PERSON><PERSON>op<PERSON>,
} from 'vant'

import { computed, defineOptions, nextTick, onBeforeMount, onBeforeUnmount, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import CameraView from '@/views/component/CameraView/index.vue'
import hiddenDangers from '@/views/component/SecurityCertificate/OverallHiddenDangers/index.vue'
import photoSignature from '@/views/component/SecurityCertificate/photoSignature/index.vue'
import GasDevice from '@/views/component/SecurityCertificate/slots/GasDevice.vue'
// 导入插槽组件
import ImagePreview from '@/views/component/SecurityCertificate/slots/ImagePreview.vue'
import StatisticsChart from '@/views/component/SecurityCertificate/slots/StatisticsChart.vue'
import userInfo from '@/views/component/SecurityCertificate/userInfo/index.vue'

import SecurityFormItem from '@/views/component/SecurityFormItem/index.vue'
import XSelect from '@/views/component/XSelect/index.vue'
import { mobileUtil } from '@/views/example/example1/mobileUtil'
import useBusinessStore from '@/stores/modules/business'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'

// 导入UserDetail组件的API方法
import { getCacheUserDetail } from '@af-mobile-client-vue3/components/data/UserDetail/index.vue'

defineOptions({ name: 'SecurityCertificate' })
// 获取路由实例
const router = useRouter()

// 组件映射
const slotComponentMap = {
  imagePreview: ImagePreview,
  statisticsChart: StatisticsChart,
  gasDevice: GasDevice,
}

// 浮动气泡
const offset = ref({ x: 310, y: 500 })
const floatingBubbleStatus = ref(false)

// 计算属性，从config.data中获取itemname作为下拉菜单的选项，并添加错误计数
const actions = ref([
  { text: '用户信息', value: 0, icon: '', errorCount: 0 },
])

const config = reactive({ data: [
  {
    itemname: '燃气表',
    level: '1号',
    checkmust: false,
    type: 'checkItem',
    classification: '14',
    items: [
      {
        itemname: '火眼数',
        level: '级别3',
        f_process_mode: '现场整改',
        options: [],
        checkmust: 'false',
        type: 'text',
        f_aging: null,
      },
      {
        itemname: '左右表',
        level: '级别1',
        f_process_mode: '自行处理',
        options: [
          {
            isdefect: false,
            data: '左表',
            isdefault: false,
          },
          {
            isdefect: false,
            data: '右表',
            isdefault: false,
          },
        ],
        checkmust: 'false',
        type: 'selector',
        f_aging: null,
      },
      {
        itemname: '是否告知用户定期更换点火胶管',
        level: '级别3',
        f_process_mode: '现场整改',
        options: [
          {
            isdefect: false,
            data: '是',
            isdefault: true,
          },
          {
            isdefect: false,
            data: '否',
            isdefault: false,
          },
        ],
        checkmust: 'true',
        type: 'radio',
        f_aging: null,
      },
      {
        itemname: '表封颜色',
        level: '级别3',
        f_process_mode: '现场整改',
        options: [
          {
            isdefect: false,
            data: '黄',
            isdefault: false,
          },
          {
            isdefect: false,
            data: '红',
            isdefault: true,
          },
          {
            isdefect: false,
            data: '蓝',
            isdefault: false,
          },
        ],
        checkmust: 'true',
        type: 'selector',
        f_aging: null,
      },
      {
        itemname: '是否安装防爆灯',
        level: '级别3',
        f_process_mode: '自行处理',
        options: [
          {
            isdefect: false,
            data: '是',
            isdefault: true,
          },
          {
            isdefect: false,
            data: '否',
            isdefault: false,
          },
        ],
        checkmust: 'true',
        type: 'radio',
        f_aging: null,
        classification: null,
      },
    ],
  },
  {
    itemname: '燃气设施检查',
    checkmust: false,
    type: 'checkItem',
    items: [
      {
        itemname: '火眼数',
        level: '级别3',
        f_process_mode: '现场整改',
        options: [],
        checkmust: 'false',
        type: 'number',
        f_aging: null,
      },
      {
        itemname: '吨位数',
        level: '级别3',
        f_process_mode: '现场整改',
        options: [],
        checkmust: 'false',
        type: 'number',
        f_aging: null,
      },
      {
        itemname: '锅炉台数',
        level: '级别3',
        f_process_mode: '自行处理',
        options: [],
        checkmust: 'false',
        type: 'string',
        f_aging: '1',
      },
      {
        itemname: '备用锅炉台数',
        level: '级别3',
        f_process_mode: '自行处理',
        options: [],
        checkmust: 'false',
        type: 'string',
        f_aging: '1',
      },
      {
        itemname: '天然气气密性是否漏气',
        level: '级别1',
        f_process_mode: '现场整改',
        options: [
          {
            isdefect: true,
            data: '是',
            isdefault: false,
          },
          {
            isdefect: false,
            data: '否',
            isdefault: true,
          },
        ],
        checkmust: 'true',
        type: 'radio',
        f_aging: null,
      },
      {
        itemname: '总承铅封',
        level: '级别3',
        f_process_mode: '自行处理',
        options: [
          {
            isdefect: false,
            data: '有',
            isdefault: true,
          },
          {
            isdefect: false,
            data: '无',
            isdefault: false,
          },
        ],
        checkmust: 'true',
        type: 'radio',
        f_aging: null,
      },
      {
        itemname: '螺栓铅封',
        level: '级别3',
        f_process_mode: '现场整改',
        options: [
          {
            isdefect: false,
            data: '有',
            isdefault: true,
          },
          {
            isdefect: false,
            data: '无',
            isdefault: false,
          },
        ],
        checkmust: 'true',
        type: 'radio',
        f_aging: null,
      },
      {
        itemname: '气表铅封',
        level: '级别3',
        f_process_mode: '自行处理',
        options: [
          {
            isdefect: false,
            data: '是',
            isdefault: true,
          },
          {
            isdefect: false,
            data: '否',
            isdefault: false,
          },
        ],
        checkmust: 'false',
        type: 'radio',
        f_aging: null,
      },
      {
        itemname: '气表运行正常',
        level: '级别1',
        f_process_mode: '现场整改',
        options: [
          {
            isdefect: false,
            data: '是',
            isdefault: true,
          },
          {
            isdefect: true,
            data: '否',
            isdefault: false,
          },
        ],
        checkmust: 'true',
        type: 'radio',
        f_aging: null,
      },
      {
        itemname: '室外立管是否有泄露报警',
        level: '级别3',
        f_process_mode: '现场整改',
        options: [
          {
            isdefect: true,
            data: '是',
            isdefault: false,
          },
          {
            isdefect: false,
            data: '否',
            isdefault: true,
          },
        ],
        checkmust: 'true',
        type: 'radio',
        f_aging: null,
      },
      {
        itemname: '气表运行是否正常',
        level: '级别1',
        f_process_mode: '现场整改',
        options: [
          {
            isdefect: false,
            data: '正常',
            isdefault: false,
          },
          {
            isdefect: true,
            data: '不正常',
            isdefault: false,
          },
        ],
        checkmust: 'true',
        type: 'radio',
        f_aging: null,
      },
      {
        itemname: '是否擅自改动燃气管道及设施',
        level: '级别1',
        f_process_mode: '自行处理',
        options: [
          {
            isdefect: true,
            data: '是',
            isdefault: false,
          },
          {
            isdefect: false,
            data: '否',
            isdefault: true,
          },
        ],
        checkmust: 'true',
        type: 'radio',
        f_aging: null,
      },
      {
        itemname: '室内支管是否有泄露报警',
        level: '级别3',
        f_process_mode: '现场整改',
        options: [
          {
            isdefect: true,
            data: '是',
            isdefault: false,
          },
          {
            isdefect: false,
            data: '否',
            isdefault: true,
          },
        ],
        checkmust: 'true',
        type: 'radio',
        f_aging: null,
      },
    ],
  },
  {
    slotName: 'imagePreview',
    itemname: '设备信息',
    type: 'slot',
    checkmust: false,
    items: [],
  },
  {
    slotName: 'gasDevice',
    itemname: '用气设备',
    type: 'slot',
    checkmust: false,
    items: [],
  },
  {
    slotName: 'statisticsChart',
    itemname: '测试插槽1',
    type: 'slot',
    checkmust: false,
    items: [],
  },
  {
    slotName: '',
    itemname: '测试安检项1',
    level: '级别1',
    type: 'checkItem',
    checkmust: false,
    classification: '1',
    items: [
      {
        itemname: '气表运行是否正常',
        level: '级别1',
        f_process_mode: '现场整改',
        options: [
          {
            isdefect: false,
            data: '正常',
            isdefault: false,
          },
          {
            isdefect: true,
            data: '不正常',
            isdefault: false,
          },
        ],
        checkmust: 'true',
        type: 'radio',
        f_aging: null,
      },
      {
        itemname: '设备数',
        level: '级别3',
        f_process_mode: '现场整改',
        options: [],
        checkmust: 'true',
        type: 'number',
        f_aging: null,
      },
      {
        itemname: '室外立管是否有泄露报警',
        level: '级别3',
        f_process_mode: '现场整改',
        options: [
          {
            isdefect: true,
            data: '是',
            isdefault: false,
          },
          {
            isdefect: false,
            data: '否',
            isdefault: true,
          },
        ],
        checkmust: 'true',
        type: 'radio',
        f_aging: null,
      },
      {
        itemname: '引入管是否有泄露报警',
        level: '级别3',
        f_process_mode: '自行处理',
        options: [
          {
            isdefect: true,
            data: '是',
            isdefault: false,
          },
          {
            isdefect: false,
            data: '否',
            isdefault: true,
          },
        ],
        checkmust: 'true',
        type: 'radio',
        f_aging: null,
      },
      {
        itemname: '室内支管是否有泄露报警',
        level: '级别3',
        f_process_mode: '现场整改',
        options: [
          {
            isdefect: true,
            data: '是',
            isdefault: false,
          },
          {
            isdefect: false,
            data: '否',
            isdefault: true,
          },
        ],
        checkmust: 'true',
        type: 'radio',
        f_aging: null,
      },
      {
        itemname: '表封颜色',
        level: '级别3',
        f_process_mode: '现场整改',
        options: [
          {
            isdefect: false,
            data: '黄',
            isdefault: false,
          },
          {
            isdefect: false,
            data: '红',
            isdefault: true,
          },
          {
            isdefect: false,
            data: '蓝',
            isdefault: false,
          },
        ],
        checkmust: 'true',
        type: 'selector',
        f_aging: null,
        classification: null,
      },
    ],
  },
] })

const dataStructure = {
  fieldType: 'type', // 表单项类型
  fieldLabel: 'itemname',
  fieldValue: 'value',
  placeholder: 'itemname',
  fieldOptions: 'options',
  optionsLabel: 'data',
  optionsValue: 'data',
}

// 悬浮气泡菜单项
const floatingBubbleActions = ref([])

const status = ref(false)
onBeforeMount(() => {
  runLogic('getSecurityCheckTypeHistoryById', { id: 2 }).then((res: any) => {
    if (res && res.f_json) {
      // config.data = JSON.parse(res.f_json) // 原始数据，注释掉使用测试数据
      updateActions()
      status.value = true
    }
  })
  init()
})
function init() {
  getConfigByName('CheckSmallToolConfig', (result) => {
    floatingBubbleActions.value = [...result.toolList, { title: '返回首页', icon: 'home-o', routerName: '返回首页' }]
  })
}
// 修改为使用对象管理多个表单引用
const formRefs = ref<FormInstance[]>([])
// 当前选中的tab索引
const activeTabIndex = ref(0)
// 子组件实例
const hiddenDangersRef = ref(null)
const photoSignatureRef = ref(null)

// 添加错误计数追踪
const errorCounts = reactive({
  userInfo: 0,
  tabs: [] as number[],
  hiddenDangers: 0,
  photoSignature: 0,
})

// 确保所有标签页被初始化的函数
async function initAllTabs() {
  const currentTab = activeTabIndex.value

  // 遍历激活每个标签页
  for (let i = 0; i < actions.value.length; i++) {
    activeTabIndex.value = i
    await nextTick() // nextTick确保DOM完全更新
  }

  // 恢复原来的标签页
  activeTabIndex.value = currentTab
  await nextTick()
}

// 当状态变为true时（数据加载完成后）初始化所有标签页
watch(() => status.value, async (newVal) => {
  if (newVal) {
    await nextTick()
    await initAllTabs()
    // 初始化错误计数数组
    errorCounts.tabs = Array.from({ length: config.data.length }).fill(0) as number[]
  }
})

// 验证所有表单并更新错误计数
async function validateAllForms() {
  // 重置所有错误计数
  errorCounts.userInfo = 0
  errorCounts.tabs.fill(0)
  errorCounts.hiddenDangers = 0
  errorCounts.photoSignature = 0

  // 存储错误信息，以便在提示时使用
  let firstErrorMessage = ''

  // 再次确保所有标签页已初始化
  await initAllTabs()

  // 验证常规表单，跳过slot类型的配置项
  let formIndex = 0
  for (let i = 0; i < config.data.length; i++) {
    const configItem = config.data[i]
    // 跳过slot类型的配置项，因为它们不需要表单验证
    if (configItem.type === 'slot') {
      continue
    }

    const formInstance = formRefs.value[formIndex]
    if (formInstance) {
      try {
        await formInstance.validate()
      }
      catch (error) {
        // 更新错误计数，使用实际的表单索引
        errorCounts.tabs[i] = error.length || 1
        // 记录第一条错误信息
        if (!firstErrorMessage && error && error[0])
          firstErrorMessage = error[0]
      }
    }
    formIndex++
  }
  // 验证整体隐患组件
  if (hiddenDangersRef.value) {
    try {
      await hiddenDangersRef.value.validate()
    }
    catch (error) {
      errorCounts.hiddenDangers = error.length || 1
      // 记录第一条错误信息
      if (!firstErrorMessage && error && error[0])
        firstErrorMessage = error[0]
    }
  }
  // 验证拍照签名组件
  if (photoSignatureRef.value) {
    try {
      await photoSignatureRef.value.validate()
    }
    catch (error) {
      errorCounts.photoSignature = error.length || 1
      // 如果还没有记录错误信息，记录拍照签名的第一条错误
      if (!firstErrorMessage && error && error[0])
        firstErrorMessage = error[0]
    }
  }

  return {
    hasErrors: errorCounts.userInfo > 0 || errorCounts.tabs.some(count => count > 0) || errorCounts.photoSignature > 0 || errorCounts.hiddenDangers > 0,
    firstErrorMessage,
  }
}

// 提交表单时的处理逻辑
async function onSubmit() {
  try {
    // 验证所有表单并更新错误计数
    const { hasErrors, firstErrorMessage } = await validateAllForms()

    if (hasErrors) {
      // 找到第一个有错误的标签页并跳转
      if (errorCounts.userInfo > 0) {
        activeTabIndex.value = 0
      }
      else {
        const firstErrorIndex = errorCounts.tabs.findIndex(count => count > 0)
        if (firstErrorIndex >= 0)
          activeTabIndex.value = firstErrorIndex + 1 // +1 因为第0个是用户信息
        else if (errorCounts.hiddenDangers > 0)
          activeTabIndex.value = actions.value.length - 2
        else if (errorCounts.photoSignature > 0)
          activeTabIndex.value = actions.value.length - 1
      }

      // 显示具体的错误信息，而不是通用提示
      showFailToast(firstErrorMessage || '请填写所有的必填项')
      return
    }

    // 如果所有表单校验通过，则弹出确认框
    showConfirmDialog({
      title: '服务',
      message: '请确认是否提交？',
    })
      .then(() => {
        // 用户确认提交
        history.back()
      })
      .catch(() => {
        // 用户取消提交
      })
  }
  catch (error) {
    // 如果有表单校验未通过，则提示用户
    console.error('表单校验未通过:', error)
    showFailToast('请填写所有的必填项！！！')
  }
}

// 更新actions数据的函数
function updateActions() {
  // 清空actions，保留第一个"用户信息"选项
  actions.value = [{ text: '用户信息', value: 0, icon: '', errorCount: 0 }]

  // 从config.data中获取itemname，添加到actions中
  config.data.forEach((item, index) => {
    actions.value.push({
      text: item.itemname,
      value: index + 1, // 索引+1，因为第0个是"用户信息"
      icon: '', // 添加空的icon属性以满足类型要求
      errorCount: 0,
    })
  })
  actions.value.push({
    text: '整体隐患',
    value: actions.value.length, // 倒数第二
    icon: '', // 添加空的icon属性以满足类型要求
    errorCount: 0,
  })
  actions.value.push({
    text: '拍照签名',
    value: actions.value.length + 1, // 最后一个
    icon: '', // 添加空的icon属性以满足类型要求
    errorCount: 0,
  })
}

// 添加处理方式选项常量
const columns = [
  { label: '现场处理', value: '现场处理' },
  { label: '自行整改', value: '自行整改' },
  { label: '转维修', value: '转维修' },
]

// 更新actions中的错误计数的计算属性
const updatedActions = computed(() => {
  return actions.value.map((action, index) => {
    if (index === 0) {
      // 用户信息
      return {
        ...action,
        errorCount: errorCounts.userInfo,
      }
    }
    else if (index === actions.value.length - 2) {
      // 整体隐患
      return {
        ...action,
        errorCount: errorCounts.hiddenDangers,
      }
    }
    else if (index === actions.value.length - 1) {
      // 拍照签名
      return {
        ...action,
        errorCount: errorCounts.photoSignature,
      }
    }
    else {
      // 其他标签页，需要检查是否为slot类型
      const configIndex = index - 1 // 因为第0个是用户信息
      const configItem = config.data[configIndex]

      // 如果是slot类型，错误计数始终为0
      if (configItem && configItem.type === 'slot') {
        return {
          ...action,
          errorCount: 0,
        }
      }

      // 其他类型使用正常的错误计数
      return {
        ...action,
        errorCount: errorCounts.tabs[configIndex] || 0,
      }
    }
  })
})

function valueChange(selVal, selObj, currentVal) {
  switch (currentVal.type) {
    case 'radio':
    case 'select':
    case 'checkbox':
    case 'multipleSelect':
      currentVal.f_is_defect = selObj[0].isdefect
      break
  }
}

function processModeChange(selVal, selObj) {
  // 处理方式变更
}

function cameraViewChange(list) {
  // 相机视图变更
}

function change(value) {
  // 设置当前选中的tab索引
  activeTabIndex.value = value
}

const checked2 = ref([])

function audio(isRecord: boolean) {
  if (isRecord) {
    mobileUtil.execute({
      funcName: 'makePhoneCall',
      param: {
        phoneNumber: '13209745328',
      },
      callbackFunc: (result) => {
        console.log('reslut====', JSON.stringify(result))
      },
    })
  }
  else {
    mobileUtil.execute({
      funcName: 'showSignaturePad',
      param: {},
      callbackFunc: (result) => {
        console.log('reslut====', JSON.stringify(result))
      },
    })
  }
}

// 获取 van-tabs 的实例
const tabsRef = ref(null)

function slideLeft() {
  if (activeTabIndex.value === 0)
    activeTabIndex.value = actions.value.length - 1
  else
    activeTabIndex.value = activeTabIndex.value - 1
  if (tabsRef.value)
    (tabsRef.value as any).scrollTo(activeTabIndex.value)
}

function slideRight() {
  if (activeTabIndex.value === actions.value.length - 1) {
    showFailToast('已经是最后一页了')
  }
  else {
    activeTabIndex.value = activeTabIndex.value + 1
    if (tabsRef.value)
      (tabsRef.value as any).scrollTo(activeTabIndex.value)
  }
}

// 气泡弹出层按钮状态
const tabStatus = ref(false)

function PageSwitch(event) {
  // 设置当前选中的tab索引
  activeTabIndex.value = event.value
  if (tabsRef.value)
    (tabsRef.value as any).scrollTo(event.value)
}

// 气泡弹出层的图标
const popoverIcon = computed(() => {
  return tabStatus.value ? 'arrow-up' : 'arrow-down'
})

// 底部按钮的样式
const buttonType = computed(() => {
  return activeTabIndex.value === actions.value.length - 1 ? 'success' : 'primary'
})

const buttonText = computed(() => {
  return activeTabIndex.value === actions.value.length - 1 ? '保 存' : '下一页'
})

const buttonIcon = computed(() => {
  return activeTabIndex.value === actions.value.length - 1 ? 'success' : 'arrow'
})

// 菜单位置计算
const menuPosition = computed(() => {
  // 气泡直径和菜单宽度
  const bubbleSize = 55
  const menuWidth = 140

  // 计算菜单位置，使菜单居中在气泡上方
  // 确保菜单左侧与气泡中心对齐
  const menuLeft = offset.value.x - (menuWidth / 2) + (bubbleSize / 2)

  // 添加安全距离，确保菜单不会超出屏幕边缘
  const safeMenuLeft = Math.max(10, Math.min(window.innerWidth - menuWidth - 10, menuLeft))

  return {
    left: `${safeMenuLeft}px`,
    top: `${offset.value.y - 100}px`, // 减少间距，让菜单更靠近气泡
    position: 'fixed' as const,
    zIndex: 2001,
  }
})

// 浮动气泡点击事件
function onFloatingBubble() {
  floatingBubbleStatus.value = !floatingBubbleStatus.value
}

function floatingBubbleSwitch(action) {
  // 返回首页固定为路由回退
  if (action.title === '返回首页') {
    router.back()
  }
  // 其他路由固定为路由页面跳转携带参数
  else if (action.routerName) {
    router.push({
      path: action.routerName,
      query: {
        f_userinfo_id: '65836',
      },
    })
  }
  else {
    showFailToast('功能开发中')
  }
  // 关闭气泡
  floatingBubbleStatus.value = false
}

// 添加点击外部关闭菜单的功能
watch(() => floatingBubbleStatus.value, (newVal) => {
  if (newVal) {
    // 延迟添加事件，避免同一次点击立即关闭
    setTimeout(() => {
      document.addEventListener('click', handleOutsideClick)
    }, 10)
  }
  else {
    document.removeEventListener('click', handleOutsideClick)
  }
})

function handleOutsideClick(e) {
  const bubble = document.querySelector('.van-floating-bubble')
  const menu = document.querySelector('.bubble-menu-container')

  // 如果点击不在气泡或菜单内，则关闭菜单
  if (bubble && !bubble.contains(e.target) && menu && !menu.contains(e.target))
    floatingBubbleStatus.value = false
}

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('click', handleOutsideClick)
})
</script>

<template>
  <div v-if="status" class="security-certificate-container">
    <div class="tabs-container">
      <!-- 标签页容器 -->
      <div class="tabs-wrapper">
        <van-tabs ref="tabsRef" v-model:active="activeTabIndex" :ellipsis="false" sticky class="custom-tabs">
          <van-tab>
            <template #title>
              <div class="tab-title">
                <span>基本信息</span>
                <span v-if="errorCounts.userInfo > 0" class="error-badge">{{ errorCounts.userInfo }}</span>
              </div>
            </template>
            <div class="x-form-group">
              <userInfo />
            </div>
          </van-tab>
          <template v-for="(row, index) in config.data" :key="row.itemname + index">
            <van-tab>
              <template #title>
                <div class="tab-title">
                  <span>{{ row.itemname }}</span>
                  <span v-if="row.type !== 'slot' && errorCounts.tabs[index] > 0" class="error-badge">{{ errorCounts.tabs[index] }}</span>
                </div>
              </template>

              <!-- 根据type字段渲染不同内容 -->
              <div v-if="row.type === 'slot'" class="x-form-group-item">
                <!-- 动态渲染自定义组件 -->
                <div class="slot-component">
                  <component
                    :is="slotComponentMap[row.slotName as string]"
                    v-if="row.slotName && slotComponentMap[row.slotName as string]"
                    :config="row"
                  />
                  <div v-else class="default-slot-container">
                    <h3>{{ row.itemname }}</h3>
                    <p>自定义插槽内容 - {{ row.slotName }}</p>
                  </div>
                </div>
              </div>

              <!-- 原有的checkItem类型渲染逻辑 -->
              <div v-else class="x-form-group-item">
                <VanForm :ref="(el) => { if (el) formRefs[index] = el as any }" @submit="onSubmit">
                  <van-cell-group>
                    <div class="x-form-title">
                      {{ row.itemname }}
                    </div>
                    <template v-for="(item, index) in row.items" :key="row.itemname + index">
                      <SecurityFormItem
                        :item="item"
                        :data-structure="dataStructure"
                        @value-change="valueChange"
                      >
                        <template v-if="(item as any).f_is_defect === true" #default="{ item }">
                          <van-field
                            name="handlingMethod"
                            center
                            label="处理方式"
                            :rules="[{ required: item.checkmust === 'true', message: '请选择处理方式' }]"
                          >
                            <template #input>
                              <XSelect
                                v-model="item.f_process_mode"
                                placeholder="请选择处理方式"
                                :columns="columns"
                                @confirm="processModeChange"
                              />
                            </template>
                          </van-field>
                          <van-field
                            name="picture"
                            center
                            label="隐患照片"
                            :rules="[{ required: item.checkmust === 'true', message: '请上传隐患照片' }]"
                          >
                            <template #input>
                              <CameraView :photo-list="item.f_defect_paths" @change="cameraViewChange" />
                            </template>
                          </van-field>
                        </template>
                      </SecurityFormItem>
                    </template>
                  </van-cell-group>
                </VanForm>
              </div>
            </van-tab>
          </template>
          <van-tab>
            <template #title>
              <div class="tab-title">
                <span>整体隐患</span>
                <span v-if="errorCounts.hiddenDangers > 0" class="error-badge">{{ errorCounts.hiddenDangers }}</span>
              </div>
            </template>
            <div class="x-form-group">
              <hiddenDangers ref="hiddenDangersRef" />
            </div>
          </van-tab>
          <van-tab>
            <template #title>
              <div class="tab-title">
                <span>拍照签名</span>
                <span v-if="errorCounts.photoSignature > 0" class="error-badge">{{ errorCounts.photoSignature }}</span>
              </div>
            </template>
            <div class="x-form-group">
              <photoSignature ref="photoSignatureRef" />
            </div>
          </van-tab>
        </van-tabs>
      </div>
      <!-- 固定在标签页标题行右侧的 VanPopover -->
      <div class="fixed-popover-container">
        <VanPopover
          v-model:show="tabStatus"
          :actions="updatedActions"
          placement="bottom-end"
          @select="PageSwitch($event)"
        >
          <template #reference>
            <VanButton :icon="popoverIcon" icon-position="left" type="default" size="mini" class="custom-button" />
          </template>
          <template #action="{ action }">
            <div class="custom-popover-action" :class="{ active: action.value === activeTabIndex }">
              <span class="popover-action-text">{{ action.text }}</span>
              <span v-if="action.errorCount > 0" class="error-badge">{{ action.errorCount }}</span>
            </div>
          </template>
        </VanPopover>
      </div>
    </div>
    <!-- 新增底部按钮区域 -->
    <div class="footer-buttons">
      <VanButton icon="passed" type="primary" class="submit-btn">
        提交表单
      </VanButton>
      <div class="slide-buttons">
        <VanButton type="default" plain icon="arrow-left" text="上一步" @click="slideLeft" />
        <VanButton :type="buttonType" normal @click="slideRight">
          {{ buttonText }}<VanIcon :name="buttonIcon" />
        </VanButton>
      </div>
    </div>

    <!-- 悬浮气泡 -->
    <FloatingBubble
      v-model:offset="offset"
      icon="bag-o"
      axis="xy"
      magnetic="x"
      @click="onFloatingBubble"
    />

    <!-- 自定义悬浮菜单 -->
    <teleport to="body">
      <div v-if="floatingBubbleStatus" class="bubble-menu-container" :style="menuPosition">
        <div class="bubble-menu">
          <div
            v-for="(action, index) in floatingBubbleActions"
            :key="index"
            class="bubble-menu-item"
            @click="floatingBubbleSwitch(action)"
          >
            <VanIcon :name="action.icon" class="bubble-menu-icon" />
            <span>{{ action.title }}</span>
          </div>
        </div>
      </div>
    </teleport>
  </div>
</template>

<style scoped lang="less">
.security-certificate-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.tabs-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative; /* 使 fixed-popover-container 相对于 tabs-container 定位 */
  padding-bottom: 80px; /* 为底部按钮留出空间 */
}

.tabs-wrapper {
  width: 100%; /* 确保 van-tabs 占据整个宽度 */
  background-color: #f7f8fa;
  padding-bottom: 10px;
}

.x-form-group {
  margin: 0;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

:deep(.custom-tabs > .van-tabs__content > .van-tab__panel) {
  padding: 4px 10px;
}

.custom-tabs {
  :deep(.van-tabs__nav) {
    padding-right: 45px; /* 在标题栏右侧留出空间 */
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.van-tab) {
    padding-right: 10px; /* 为标题右侧的徽标留出更多空间 */
  }

  :deep(.van-tabs__line) {
    background-color: #1989fa;
    height: 3px;
  }
}

.x-form-title {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  background-color: #fff;
  padding: 15px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.fixed-popover-container {
  position: absolute; /* 绝对定位 */
  top: 5px; /* 与标签页标题行顶部对齐 */
  right: 0; /* 固定在右侧 */
  z-index: 1000; /* 确保在其他内容之上 */
  margin-right: -1px; /* 右侧间距 */
}

.footer-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  position: fixed; /* 固定位置 */
  bottom: 0; /* 距离底部0像素 */
  left: 0;
  right: 0;
  width: 100%; /* 宽度占满整个页面 */
  background-color: white; /* 背景颜色，防止内容被遮挡 */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* 添加阴影，提升视觉效果 */
  z-index: 1000;

  .submit-btn {
    flex: 1;
    margin-right: 12px;
    height: 44px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
  }

  .slide-buttons {
    display: flex;
    gap: 8px;

    .van-button {
      height: 44px;
      border-radius: 8px;
      font-size: 14px;
    }
  }
}

.custom-button {
  border: none !important;
  box-shadow: none !important;
  pointer-events: none; // 禁用点击反馈
  color: #666;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;

  :deep(.van-icon) {
    vertical-align: middle;
    margin-right: 6px;
    color: #666;
  }
}

.error-badge {
  display: inline-block;
  background-color: #ff4757;
  color: #fff;
  border-radius: 50%;
  font-size: 12px;
  height: 16px;
  width: 16px;
  line-height: 16px;
  text-align: center;
  margin-left: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* 添加阴影增强可见性 */
}

.custom-popover-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8px 0;
  border-radius: 8px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f7f8fa;
  }

  .popover-action-text {
    flex: 1;
    text-align: left;
    margin-right: 12px;
    font-size: 14px;
    font-weight: 500;
    color: #323233;
    line-height: 1.4;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
  }

  &.active {
    .popover-action-text {
      color: #1989fa;
      font-weight: 600;
    }
  }

  .error-badge {
    font-size: 12px;
    height: 16px;
    width: 16px;
    line-height: 16px;
    flex-shrink: 0; /* 防止徽标被挤压变形 */
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(255, 0, 0, 0.3);
    animation: pulse 2s infinite;
  }
}

/* 添加脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

:deep(.van-popover__action) {
  height: auto;
}

:deep(.van-popover__action-text) {
  justify-content: left !important;
}

.tab-title {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0 3px; /* 添加一些内边距 */
  min-height: 20px; /* 确保标题有足够高度 */

  .error-badge {
    position: absolute;
    top: -2px; /* 微调顶部位置 */
    right: -10px; /* 微调右侧位置 */
    margin-left: 0;
    z-index: 10; /* 确保徽标始终显示在顶层 */
    font-size: 10px; /* 右上角位置徽标稍微小一点 */
    height: 14px;
    width: 14px;
    line-height: 14px;
  }
}

/* 气泡样式 */
:deep(.van-floating-bubble) {
  --van-floating-bubble-size: 55px;
  --van-floating-bubble-background: #1989fa;
  --van-floating-bubble-color: #fff;
  z-index: 999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

:deep(.van-floating-bubble__icon) {
  font-size: 26px;
}

/* 气泡菜单样式 */
.bubble-menu-container {
  position: fixed;
  z-index: 2000;
}

.bubble-menu {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  min-width: 140px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
}

.bubble-menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 20px;
  color: #323233;
  cursor: pointer;

  &:active {
    background-color: #f2f3f5;
  }

  &:not(:last-child) {
    border-bottom: 1px solid #ebedf0;
  }
}

.bubble-menu-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #1989fa;
}

/* 插槽组件样式 */
.slot-component {
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  margin: 8px 0;
}

.default-slot-container {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f7f8fa;
  border: 1px dashed #dcdee0;
  border-radius: 6px;
  padding: 20px;

  h3 {
    color: #1989fa;
    margin-bottom: 8px;
  }
}

/* 表单字段样式调整 */
:deep(.van-field) {
  //padding: 16px 20px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .van-field__label {
    color: #333;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .van-field__value {
    color: #666;
    font-size: 14px;
  }

  .van-field__control {
    color: #333;
    font-size: 14px;
  }

  .van-field__placeholder {
    color: #ff4757;
    font-size: 14px;
  }
}

/* 必填字段样式 */
:deep(.van-field--required) {
  .van-field__label::before {
    content: '*';
    color: #ff4757;
    margin-right: 4px;
  }
}

/* 选择器样式 */
:deep(.van-cell) {
  //padding: 16px 20px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .van-cell__title {
    color: #333;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .van-cell__value {
    color: #666;
    font-size: 14px;
  }
}
:deep(.van-cell) {
  border-bottom: none;
}

/* 表单组样式 */
.x-form-group-item {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin: 0;
  overflow: hidden;
}
/* VanPopover 弹出内容区域样式 */

:deep(.van-popover__content) {
  padding: 12px;
  background: rgba(254, 254, 254, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: none;
}

:deep(.van-popover__arrow) {
  border-color: #fff !important;
}
</style>
