<script setup lang="ts">
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { runLogic } from '@af-mobile-client-vue3/services/api/common'
import { post } from '@af-mobile-client-vue3/services/restTools'
import useUserStore from 'af-mobile-client-vue3/src/stores/modules/user'
import { showDialog, showToast } from 'vant'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import useBusinessStore from '@/stores/modules/business'
import { formatTime } from '@/utils/date'
import 'vant/es/toast/style'
import 'vant/es/dialog/style'

const emit = defineEmits(['closeOperation', 'complete'])
// 获取全局状态
const businessStore = useBusinessStore()
const route = useRoute()
const userInfo = computed(() => businessStore.currentUser)
const currUser = useUserStore().getLogin().f
// 组件状态
const loading = ref(false)
// 用户信息加载状态
const userLoading = ref(false)

// 阀门状态与控制模式
const valveStatus = ref(userInfo.value?.f_valve_state)
const controlMode = ref(userInfo.value?.f_network_valve === '0' ? '自动' : '手动')

// 对话框显示状态
const showConfirmDialog = ref(false)

// 操作原因
const operationReason = ref('')

// 操作记录
const operationRecords = ref<any[]>([])

// 获取用户信息的方法（按照图片中的方式）
async function getUserInfo(userInfoId: string) {
  try {
    userLoading.value = true

    // 方法1：使用runLogic调用后端接口获取用户信息
    try {
      const userDetailResult = await runLogic('getUserDetailById', {
        userInfoId: userInfoId
      }, 'af-revenue')

      if (userDetailResult) {
        businessStore.setCurrentUser(userDetailResult)
        console.log('用户信息已设置到businessStore:', userDetailResult)
        return userDetailResult
      }
    } catch (runLogicError) {
      console.log('runLogic调用失败，尝试使用post方式:', runLogicError)

      // 方法2：使用post调用，参考securityCheckDetail.vue的方式
      const params = {
        queryParamsName: 'GetUserDetailCRUD',
        pageNo: 1,
        pageSize: 20,
        conditionParams: {
          f_userinfo_id: userInfoId,
        },
      }

      const result = await post('/api/af-revenue/logic/commonQuery', params)
      if (result && result.data && result.data.length > 0) {
        const userData = result.data[0]
        businessStore.setCurrentUser(userData)
        console.log('用户信息已设置到businessStore:', userData)
        return userData
      } else {
        throw new Error('未找到用户信息')
      }
    }

  } catch (error) {
    console.error('获取用户信息失败:', error)
    showToast('获取用户信息失败')
    return null
  } finally {
    userLoading.value = false
  }
}

// 初始化组件
onMounted(async () => {
  try {
    // 检查是否有用户数据，如果没有则尝试从路由参数获取
    if (!businessStore.currentUser) {
      const userInfoId = route.query.f_userinfo_id as string
      if (userInfoId) {
        console.log('从路由参数获取用户信息:', userInfoId)
        const userData = await getUserInfo(userInfoId)
        if (!userData) {
          showToast('获取用户信息失败，请重试')
          return
        }
      } else {
        console.warn('没有用户数据，请从安检页面正确进入')
        showToast('用户信息缺失，请从安检页面进入')
        return
      }
    }

    console.log('当前用户数据:', businessStore.currentUser)
    loading.value = false

    // 只有在有正确用户数据时才获取操作记录
    if (businessStore.currentUser?.f_userfiles_id) {
      getOperateRecord()
    }
  } catch (error) {
    console.error('初始化失败:', error)
    showToast('初始化失败，请重试')
  }
})

// 阀门操作相关方法
function toggleValve() {
  operationReason.value = ''
  showConfirmDialog.value = true
}

function toggleControlMode() {
  if (!userInfo.value) {
    showToast('用户信息不存在')
    return
  }

  showDialog({
    title: '提示',
    message: `确认要将用户的自动阀控状态更改为${controlMode.value === '自动' ? '手动' : '自动'}吗？`,
    showCancelButton: true,
  }).then(async () => {
    const data = {
      f_userfiles_id: userInfo.value?.f_userfiles_id,
      inputtor: currUser.resources.name,
      inputtorid: currUser.resources.id,
      f_userinfo_id: currUser.resources.id,
      meterBrandName: userInfo.value?.f_alias,
    }
    runLogic(`${controlMode.value === '自动' ? 'openzdfk' : 'closezdfk'}`, data, 'af-revenue').then((res) => {
      emit('complete', { status: true })
      showToast(`更改自动阀控状态成功`)
    }).catch((error) => {
      console.error(error)
      showToast(`更改自动阀控状态失败${error.message || error.msg || '未知错误'}`)
    })
  }).catch(() => {
  })
}

function confirmValveOperation() {
  if (!userInfo.value) {
    showToast('用户信息不存在')
    return
  }

  if (!operationReason.value) {
    showToast('请填写操作原因')
    return
  }
  const oper = valveStatus.value === '开启' ? '关阀' : '开阀'
  const data = {
    instructType: '阀门控制',
    instructTitle: `手动${oper}`,
    condition: `t_userfiles.f_userfiles_id = '${userInfo.value?.f_userfiles_id}'`,
    meterBrandName: userInfo.value?.f_alias,
    f_instruct_state: '待发送',
    inputtor: currUser.resources.name,
    reasonInfo: operationReason.value,
    meternumberf: userInfo.value?.f_meternumber,
    contentData: { isOpen: oper === '关阀' ? 1 : 0 },
    f_operat_type: '阀门控制',
    f_describe: `${currUser.resources.name}对用户${userInfo.value?.f_user_name}进行手动${oper}操作`,
    f_state: '有效',
    f_comments: operationReason.value,
    f_operate_date: formatTime(new Date()),
    f_userfiles_id: userInfo.value?.f_userfiles_id,
    f_userinfo_id: userInfo.value?.f_userinfo_id,
    f_user_id: userInfo.value?.f_user_id,
    f_orgid: currUser.resources.orgid,
    f_orgname: currUser.resources.orgs,
    f_depid: currUser.resources.depids,
    f_depname: currUser.resources.deps,
    f_operatorid: currUser.resources.id,
    f_operator: currUser.resources.name,
    f_operator_userinfo_id: currUser.resources.id,
  }
  runLogic('iot_saveInstruct', data, 'af-revenue').then((res) => {
    emit('complete', { status: true })
    showToast(`手动${oper}成功`)
  }).catch((err) => {
    console.error(err)
    showToast(`手动${oper}失败${err.message || err.msg || '未知错误'}`)
  })
}

function getOperateRecord() {
  if (!userInfo.value?.f_userfiles_id) {
    console.warn('用户信息不完整，无法获取操作记录')
    return
  }

  runLogic('mobile_getUserValveRecord', {
    f_userfiles_id: userInfo.value.f_userfiles_id,
  }, 'af-revenue').then((res) => {
    console.log('阀门操作记录:', res)
    if (res && Array.isArray(res)) {
      operationRecords.value = res
    }
    else {
      operationRecords.value = []
    }
  }).catch((error) => {
    console.error('获取阀门操作记录失败:', error)
    operationRecords.value = []
  })
}

</script>

<template>
  <div v-if="loading" class="py-8 flex items-center justify-center">
    <van-loading size="40px">
      <span class="text-4xl">加载中...</span>
    </van-loading>
  </div>
  <div>
    <!-- 当前状态卡片 -->
    <CardContainer class="iot-meter-valve-control__content">
      <CardHeader title="阀门状态" />

      <div class="p-4">
        <div class="flex items-center justify-between">
          <div class="flex min-w-0 items-center">
            <div
              class="mr-4 rounded-full flex flex-shrink-0 h-20 w-20 items-center justify-center"
              :class="{
                'bg-green-100': valveStatus === '开启',
                'bg-red-100': valveStatus === '关闭',
              }"
            >
              <div
                v-if="valveStatus === '开启'"
                class="icon-5xl i-mdi-fire text-green-600"
              />
              <div
                v-else
                class="icon-5xl i-mdi-close-circle text-red-600"
              />
            </div>
            <div class="min-w-0">
              <h5 class="text-5xl text-gray-900 font-medium truncate">
                {{ valveStatus }}
              </h5>
              <p class="text-4xl text-gray-500 truncate">
                {{ valveStatus === '开启' ? '阀门处于开启状态' : '阀门处于关闭状态' }}
              </p>
            </div>
          </div>
          <div class="ml-4 flex flex-shrink-0 items-center space-x-2">
            <van-button
              size="normal"
              class="custom-button"
              :class="controlMode === '自动' ? 'text-blue-700' : 'text-yellow-700'"
              @click="toggleControlMode()"
            >
              <div v-if="controlMode === '自动'" class="icon-5xl i-mdi-robot mr-1" />
              <div v-else class="icon-5xl i-mdi-hand mr-1" />
              <span class="text-4xl">{{ controlMode }}模式</span>
            </van-button>

            <van-button
              size="normal"
              class="custom-button"
              :type="valveStatus === '开启' ? 'danger' : 'success'"
              @click="toggleValve()"
            >
              <div v-if="valveStatus === '开启'" class="i-mdi-close-circle icon-5xl mr-1" />
              <div v-else class="i-mdi-fire icon-5xl mr-1" />
              <span class="text-4xl">{{ valveStatus === '开启' ? '关闭阀门' : '开启阀门' }}</span>
            </van-button>
          </div>
        </div>
      </div>
    </CardContainer>

    <CardContainer class="iot-meter-valve-control__content">
      <CardHeader title="操作记录" />

      <div class="p-4">
        <div class="space-y-8">
          <div
            v-for="(record, index) in operationRecords"
            :key="index"
            class="flex items-start space-x-3"
          >
            <div
              class="rounded-full flex flex-shrink-0 h-12 w-12 items-center justify-center"
              :class="{
                'bg-green-100': record.f_type === '开阀',
                'bg-red-100': record.f_type === '关阀',
                'bg-blue-100': record.f_type && record.f_type.includes('自动阀控'),
              }"
            >
              <div v-if="record.f_type === '开阀'" class="i-mdi-fire icon-6xl record-icon text-green-600" />
              <div v-else-if="record.f_type === '关阀'" class="i-mdi-close-circle icon-6xl record-icon text-red-600" />
              <div v-else-if="record.f_type && record.f_type.includes('自动阀控')" class="icon-6xl record-icon i-mdi-swap-horizontal text-blue-600" />
              <div v-else class="icon-6xl record-icon i-mdi-help-circle text-gray-600" />
            </div>

            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <p class="text-5xl text-gray-900 font-medium truncate">
                  <span v-if="record.f_type && record.f_type.includes('自动阀控')">{{ record.f_type }}</span>
                  <span v-else-if="record.f_type === '开阀'">开启阀门</span>
                  <span v-else-if="record.f_type === '关阀'">关闭阀门</span>
                  <span v-else>{{ record.f_type || '未知操作' }}</span>
                </p>
                <span class="text-4xl text-gray-500 ml-2 flex-shrink-0">{{ record.f_operate_date || '-' }}</span>
              </div>

              <p class="text-4xl text-gray-500 mt-1 truncate">
                操作人：{{ record.f_operator || '-' }}
              </p>

              <p v-if="record.f_comments" class="text-4xl text-gray-600 mt-1">
                原因：{{ record.f_comments }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </CardContainer>

    <!-- 阀门操作确认对话框 - 使用浮动弹出层 -->
    <van-popup
      v-model:show="showConfirmDialog"
      position="center"
      round
      close-on-click-overlay
      :style="{ width: '90%', maxWidth: '450px' }"
      class="valve-popup"
    >
      <div class="p-5">
        <div class="text-7xl font-bold mb-6 text-center">
          确认阀门操作
        </div>

        <div class="mb-6">
          <p class="text-6xl text-gray-600 mb-5 text-center">
            您确定要{{ valveStatus === '开启' ? '关闭' : '开启' }}阀门吗？
          </p>
          <div class="mb-6">
            <van-field
              v-model="operationReason"
              label="操作原因"
              type="textarea"
              placeholder="请输入操作原因..."
              rows="2"
              :rules="[{ required: true, message: '请填写操作原因' }]"
            />
          </div>
        </div>

        <div class="flex justify-end space-x-4">
          <van-button plain size="normal" @click="showConfirmDialog = false">
            <span class="text-5xl">取消</span>
          </van-button>
          <van-button
            type="primary"
            size="normal"
            :disabled="!operationReason"
            @click="confirmValveOperation()"
          >
            <span class="text-5xl">确认</span>
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<style scoped lang="less">
.iot-meter-valve-control {
  &__content {
    margin-bottom: 16px;
  }
}

// 组件内部的字体样式定义
.text-4xl {
  font-size: 11px;
  letter-spacing: 0.5px;
}

.text-5xl {
  font-size: 12px;
  letter-spacing: 1px;
  line-height: 1.6;
}

.text-6xl {
  font-size: 13px;
  letter-spacing: 1px;
  line-height: 1.6;
}

.text-7xl {
  font-size: 14px;
  letter-spacing: 1px;
  line-height: 1.6;
}

// 图标专用样式
.icon-4xl {
  font-size: 24px;
}

.icon-5xl {
  font-size: 28px;
}

.icon-6xl {
  font-size: 32px;
}

.icon-7xl {
  font-size: 36px;
}

// 操作记录的图标样式
.record-icon {
  margin-top: 4px;
}

.valve-popup {
  :deep(.van-field__label) {
    font-size: 12px;
    letter-spacing: 0.5px;
    margin-bottom: 6px;
  }

  :deep(.van-field__control) {
    font-size: 12px;
    letter-spacing: 0.5px;
    line-height: 1.5;
  }

  :deep(.van-popup__content) {
    padding: 8px 0;
  }

  :deep(.van-cell) {
    padding: 12px 16px;
    margin-bottom: 4px;
  }

  .mb-5 {
    margin-bottom: 20px;
  }

  .mb-6 {
    margin-bottom: 24px;
  }

  p {
    letter-spacing: 0.5px;
  }
}

:deep(.van-loading__text) {
  font-size: 12px;
}

.custom-button {
  height: auto !important;
  line-height: normal !important;
  padding: 8px 5px !important;
}
</style>
