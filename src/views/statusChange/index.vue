<script setup lang="ts">
import type { FileItem } from '@/views/component/common/FileUploader.vue'
import type { BaseUser } from '@af-mobile-client-vue3/components/data/UserDetail/types'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import UserDetail from '@af-mobile-client-vue3/components/data/UserDetail/index.vue'
import { getConfigByNameAsync, runLogic } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from 'af-mobile-client-vue3/src/stores/modules/user'
import { showFailToast } from 'vant'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import FileUploader from '@/views/component/common/FileUploader.vue'
import { tableStatusChange } from '@/services/api/business'
import useBusinessStore from '@/stores/modules/business'

import FormField from '@/views/component/common/FormField.vue'

const emit = defineEmits(['closeOperation', 'complete'])
const loading = ref(false)
const currUser = useUserStore().getLogin().f
const businessStore = useBusinessStore()
const route = useRoute()
const showReasonPicker = ref(false)
const reasonOptions = ref([])
const disableReason = ref('')

// 从路由参数获取用户ID
const userInfoId = route.query.f_userinfo_id as string
// 当前用户信息（从UserDetail组件获取）
const currentUser = ref<BaseUser | null>(null)
// UserDetail组件引用
const userDetailRef = ref(null)

// 配置对象
const config = reactive({
  fileTypes: [],
})

const formData = reactive({
  f_comments: '',
  f_othereason: '',
  files: [],
})

// 是否是启用操作
const f_operat_type = computed(() => {
  return currentUser.value?.f_table_state === '正常' ? '停用' : '启用'
})

const fileList = ref<FileItem[]>([])
const gridFileUploaderRef = ref()

// 暂时禁用宫格上传组件，只使用普通上传组件
const useGridUploader = computed(() => {
  return false // 暂时禁用
})

function onReasonConfirm(value: any) {
  formData.f_comments = value.selectedValues[0]
  showReasonPicker.value = false
}

// 监听UserDetail组件的用户数据变化
watch(() => userDetailRef.value?.user, (newUser) => {
  if (newUser) {
    currentUser.value = newUser
    businessStore.setCurrentUser(newUser)
    console.log('用户信息已从UserDetail组件获取:', newUser)

    // 用户信息加载完成后，获取禁用原因
    getDisabledReason(newUser.f_userfiles_id)
  }
}, { immediate: true })

// 获取禁用原因
async function getDisabledReason(userfilesId: string) {
  try {
    const result = await runLogic('mobile_getDisabledReason', {
      f_userfiles_id: userfilesId,
    }, 'af-revenue')
    if (result && Array.isArray(result) && result.length > 0) {
      disableReason.value = !result[0].f_othereason || result[0].f_othereason === '无'
        ? result[0].f_comments
        : result[0].f_othereason
    }
  } catch (error) {
    console.error('获取禁用原因失败:', error)
  }
}

// 初始化状态判断
onMounted(async () => {
  try {
    // 检查是否有用户ID
    if (!userInfoId) {
      console.warn('没有用户ID，请从安检页面正确进入')
      showFailToast('用户信息缺失，请从安检页面进入')
      return
    }

    console.log('用户ID:', userInfoId)

    const res = await getConfigByNameAsync(`手机端${f_operat_type.value}原因Dic`)
    // 设置原因选项
    reasonOptions.value = res.value || []

    // 获取文件上传配置
    const statusChangeConfig = await getConfigByNameAsync('mobile_statusChangeConfig')
    if (statusChangeConfig) {
      Object.assign(config, statusChangeConfig)
    }
  }
  catch (error) {
    console.error('获取配置失败:', error)
  }
})

// 取消操作
function handleCancel() {
  emit('closeOperation')
}

// 确认提交
async function handleConfirm() {
  if (!formData.f_comments) {
    showFailToast('请选择操作原因')
    return
  }

  // 验证文件上传要求
  if (useGridUploader.value && gridFileUploaderRef.value) {
    const isValid = gridFileUploaderRef.value.validateAll()
    if (!isValid) {
      return
    }
  }

  loading.value = true

  try {
    // 提取文件结果数组
    const fileResults = fileList.value.map(file => file.result?.id).filter(Boolean)
    await tableStatusChange(Object.assign(formData, {
      files: fileResults,
      f_operat_type: f_operat_type.value,
    }), businessStore.currentUser, {
      f_operator: currUser.resources.name,
      f_operatorid: currUser.resources.id,
      f_userinfo_id: currUser.resources.id,
      f_orgid: currUser.resources.orgid,
      f_orgname: currUser.resources.orgs,
      f_depid: currUser.resources.depids,
      f_depname: currUser.resources.deps,
    })
    showFailToast(`表具${f_operat_type.value}成功`)
    // 成功处理
    await emit('complete', { status: true })
  }
  catch (error) {
    console.error('操作失败:', error)
    showFailToast(`表具${f_operat_type.value}失败: ${error}`)
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="status-change-page">
    <!-- 隐藏的用户详情组件，仅用于获取用户信息 -->
    <UserDetail
      v-if="userInfoId"
      ref="userDetailRef"
      :user-info-id="userInfoId"
      :show-recent-time="false"
      :show-bottom-buttons="false"
      style="display: none;"
    />

    <CardContainer title="当前状态" class="mb-16">
      <div class="form-grid">
        <FormField label="表具状态">
          <van-field
            :model-value="currentUser?.f_table_state"
            readonly
            input-align="right"
            :class="{ 'status-normal': currentUser?.f_table_state === '正常' }"
          />
        </FormField>

        <FormField label="本次操作类型">
          <van-field
            :model-value="f_operat_type"
            readonly
            input-align="right"
            :class="{ 'status-disabled': f_operat_type === '停用' }"
          />
        </FormField>

        <FormField v-if="currentUser?.f_table_state === '停用'" label="停用原因">
          <van-field
            :model-value="disableReason"
            readonly
          />
        </FormField>
      </div>
    </CardContainer>

    <!-- 操作信息模块 -->
    <CardContainer title="操作信息" class="mb-16">
      <div class="status-change-form">
        <FormField label="操作原因">
          <van-field
            :model-value="formData.f_comments"
            readonly
            placeholder="请选择操作原因"
            is-link
            @click="showReasonPicker = true"
          />
        </FormField>

        <div class="form-remarks">
          <FormField label="备注">
            <van-field
              v-model="formData.f_othereason"
              type="textarea"
              rows="3"
              placeholder="请输入备注信息"
              maxlength="200"
              show-word-limit
              class="remarks-field"
            />
          </FormField>
        </div>

        <van-popup v-model:show="showReasonPicker" position="bottom" round teleport="body">
          <van-picker
            teleport="body"
            :columns="reasonOptions"
            show-toolbar
            title="选择操作原因"
            @confirm="onReasonConfirm"
            @cancel="showReasonPicker = false"
          />
        </van-popup>
      </div>
    </CardContainer>

    <!-- 文件上传组件 -->
    <CardContainer>
      <!-- 普通上传组件（单一文件类型或无配置时使用） -->
      <FileUploader
        v-model:file-list="fileList"
        title="上传附件"
        :multiple="true"
        :user-type="config.fileTypes && config.fileTypes.length > 0 ? config.fileTypes[0].userType : '停用/启用'"
      />
    </CardContainer>

    <!-- 按钮区域 -->
    <div class="submit-buttons">
      <van-button
        plain
        class="cancel-btn"
        :loading="loading"
        @click="handleCancel"
      >
        取消
      </van-button>
      <van-button
        type="primary"
        class="confirm-btn"
        :loading="loading"
        @click="handleConfirm"
      >
        确认提交
      </van-button>
    </div>
  </div>
</template>

<style lang="less">
/* 删除之前所有的样式，重新开始 */
</style>

<style lang="less" scoped>
/* 组件内样式 */
.status-change-page {
  position: relative;

  box-sizing: border-box;

  .info-card {
    margin-bottom: 16px;
    background-color: #fff;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .other-charge-form__section-margin {
    margin-bottom: 16px;
  }

  /* 更新按钮区域样式 */

  .submit-buttons {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  .cancel-btn {
    border-color: #d1d5db;
    color: #4b5563;
  }

  .confirm-btn {
    background-color: #2563eb;
  }

  /* 移动端样式 */
  @media screen and (max-width: 767px) {
    .submit-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  /* 平板模式样式 */
  @media screen and (min-width: 768px) {
    .submit-buttons {
      width: 85%;
      max-width: 960px;
      margin: 0 auto;
    }
  }

  .payment-section {
    margin-bottom: 16px;
  }

  .payment-header {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 12px;
    color: #333;
  }

  .payment-options {
    display: flex;
    gap: 12px;
  }

  .payment-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 12px 16px;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    background-color: #fff;
    transition: all 0.3s;
    flex: 1;

    &.active {
      border-color: #2563eb;
      background-color: #f0f7ff;
    }

    .payment-icon {
      margin-right: 8px;
    }

    .payment-name {
      font-size: 14px;
      color: #333;
    }
  }
}
</style>

<style>
/* 全局样式，设置背景色 */
.status-change-page .van-field,
.status-change-page .van-cell {
  background-color: #f9fafb !important;
}

:deep(.van-field__control) {
  text-align: right !important;
}

:deep(.van-field) {
  background-color: #f9fafb;
  border-radius: 4px;
  padding: 2px 8px !important;
  margin-bottom: 4px;
}

:deep(textarea.van-field__control) {
  text-align: left;
  padding: 8px !important;
}

:deep(input.van-field__control:not([readonly])) {
  text-align: right;
}

:deep(.van-field--readonly .van-field__control),
:deep(.van-field.van-field--is-link .van-field__control),
:deep([readonly].van-field__control),
:deep(.van-field__control[readonly]) {
  text-align: left !important;
}

:deep(.van-field--is-link .van-field__control) {
  text-align: left !important;
}

/* 确保数字字段即使是只读的也保持右对齐 */
:deep(.number-field.van-field--readonly .van-field__control) {
  text-align: right !important;
}
</style>
