import type { RouteRecordRaw } from 'vue-router'
import mixRoutes from '@af-mobile-client-vue3/router/routes'
import my from '@af-mobile-client-vue3/views/user/my/index.vue'
import SecurityCertificate from '@/views/component/SecurityCertificate/index.vue'
// 图片预览组件
import ImagePreview from '@/views/component/SecurityCertificate/slots/ImagePreview.vue'
// 统计图表组件
import StatisticsChart from '@/views/component/SecurityCertificate/slots/StatisticsChart.vue'

import test from '@/views/component/SecurityCertificate/test.vue'
import securityCheck from '@/views/core/security-check-task/securityCheck.vue'

import securityCheckDetail from '@/views/core/security-check-task/securityCheckDetail.vue'
import temporarySecurityCheck from '@/views/core/temporary-security-check/temporarySecurityCheck.vue'
import example1 from '@/views/example/example1/index.vue'
import example2 from '@/views/example/example2/index.vue'
import exampleExist from '@/views/example/exampleExist/index.vue'

import AppointmentForm from '@/views/mini/AppointmentForm.vue'
import AppointmentHistory from '@/views/mini/AppointmentHistory.vue'

import UserAppointment from '@/views/mini/UserAppointment.vue'

import reservationOrder from '@/views/reservationOrder/index.vue'
// 限购管理页面
import LimitPurchaseList from '@/views/safetyInspection/LimitPurchaseList/index.vue'

// 安检计划
import securityCheckPlan from '@/views/securityCheckPlan/index.vue'
// 新增限购设置页面
import securityCheckIndex from '@/views/securityCheckResults/index.vue'

import limitGasQuery from '@/views/securityCheckResults/limitGasQuery.vue'
import relieveForm from '@/views/securityCheckResults/relieveForm.vue'
// 安检结果
import securityCheckResults from '@/views/securityCheckResults/securityCheckResults.vue'


// 导入组件
import StatusChangePage from '@/views/StatusChangePage.vue'
// 安检结果
import userDetialView from '@/views/userDetialView/index.vue'
import ValveControlPage from '@/views/ValveControlPage.vue'

const routes: Array<RouteRecordRaw> = [

  // 在routes数组中添加路由配置
  {
    path: '/statusChange',
    name: 'statusChange',
    component: StatusChangePage,
    meta: {
      title: '停用&启用',
      keepAlive: false,
    },
  },
  {
    path: '/valveControl',
    name: 'valveControl',
    component: ValveControlPage,
    meta: {
      title: '表具阀控',
      keepAlive: false,
    },
  },
  {
    path: '/',
    name: 'reservationOrder',
    component: reservationOrder,
    meta: {
      index: 0,
    },
  },
  {
    path: '/example1',
    name: 'example1',
    component: example1,
    meta: {
      index: 0,
    },
  },
  {
    path: '/example-exist/:id',
    name: 'example-exist',
    component: exampleExist,
    meta: {
      index: 0,
    },
  },
  {
    path: '/example2',
    name: 'example2',
    component: example2,
    meta: {
      index: 1,
    },
  },
  {
    path: '/user',
    name: 'user',
    component: my,
    meta: {
      index: 2,
    },
  },
  {
    path: '/test',
    name: 'test',
    component: test,
    meta: {
      index: 0,
      title: '测试安检单填写',
    },
  },
  {
    path: '/SecurityCertificate',
    name: 'SecurityCertificate',
    component: SecurityCertificate,
    meta: {
      index: 0,
      title: '安检单填写',
    },
  },
  {
    path: '/task-home',
    name: 'task-home',
    component: securityCheck,
    meta: {
      index: 0,
      title: '测试列表',
    },
  },
  {
    path: '/temporary-security-check',
    name: 'temporary-security-check',
    component: temporarySecurityCheck,
    meta: {
      index: 0,
      title: '安检计划',
    },
  },
  {
    path: '/securityCheckDetail',
    name: 'securityCheckDetail',
    component: securityCheckDetail,
    meta: {
      index: 3,
      title: '插槽测试',
    },
  },
  {
    path: '/securityCheckResults',
    name: 'securityCheckResults',
    component: securityCheckResults,
    meta: {
      index: 3,
      title: '安检结果',
    },
  },
  {
    path: '/securityCheckPlan',
    name: 'securityCheckPlan',
    component: securityCheckPlan,
    meta: {
      index: 3,
      title: '安检计划详情',
    },
  },
  {
    path: '/user-appointment/:openid',
    name: 'user-appointment',
    component: UserAppointment,
    meta: {
      title: '用户预约',
    },
  },
  {
    path: '/appointment-form',
    name: 'appointment-form',
    component: AppointmentForm,
    meta: {
      title: '预约安检',
    },
  },
  {
    path: '/appointment-history',
    name: 'appointment-history',
    component: AppointmentHistory,
    meta: {
      title: '预约纪录',
    },
  },
  {
    path: '/ImagePreview',
    name: 'ImagePreview',
    component: ImagePreview,
    meta: {
      title: '预约纪录',
    },
  },
  {
    path: '/StatisticsChart',
    name: 'StatisticsChart',
    component: StatisticsChart,
    meta: {
      title: '预约纪录',
    },
  },
  // {
  //   path: '/testTool',
  //   name: 'testTool',
  //   component: testTool,
  //   meta: {
  //     title: '测试工具',
  //   },
  // },
  {
    path: '/userDetialView',
    name: 'userDetialView',
    component: userDetialView,
    meta: {
      title: '用户详情',
      keepAlive: false,
    },
  },
  /*  {
    path: '/reservationOrder',
    name: 'reservationOrder',
    component: reservationOrder,
    meta: {
      title: '预约待办',
    },
  }, */
  {
    path: '/:catchAll(.*)',
    redirect: {
      name: '404',
    },
  },
]

// 除了元素path 属性是 / 的元素 将公共路由全部混入
if (mixRoutes && mixRoutes.length)
  routes.push(...mixRoutes.filter(route => route.path !== '/' && route.path !== '/:catchAll(.*)'))

export default routes
